import { supabaseAdmin } from '@/lib/supabase';
import { authenticateAdminRequest } from '@/lib/admin-auth';

/**
 * API endpoint for individual product management
 * Handles GET, PUT, and DELETE operations for specific products
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 * @returns {Object} - JSON response
 */
export default async function handler(req, res) {
  // Authenticate request using our simplified auth module
  const { authorized, error, user, role } = await authenticateAdminRequest(req);
  if (!authorized) {
    return res.status(401).json({
      error: 'Unauthorized access',
      message: error?.message || 'Authentication failed'
    });
  }

  const { id } = req.query;

  // Validate product ID
  if (!id) {
    return res.status(400).json({ error: 'Product ID is required' });
  }

  // Handle different HTTP methods
  switch (req.method) {
    case 'GET':
      return getProduct(req, res, id);
    case 'PUT':
      return updateProduct(req, res, id);
    case 'DELETE':
      return deleteProduct(req, res, id);
    default:
      return res.status(405).json({ error: 'Method not allowed' });
  }
}

/**
 * Get a single product by ID
 */
async function getProduct(req, res, id) {
  try {
    // Get product from database using Supabase directly
    const { data, error } = await supabaseAdmin
      .from('products')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return res.status(404).json({ error: 'Product not found' });
      }
      throw error;
    }

    return res.status(200).json({ product: data });
  } catch (error) {
    console.error('Error fetching product:', error);
    return res.status(500).json({
      error: 'Failed to fetch product',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
}

/**
 * Update an existing product
 */
async function updateProduct(req, res, id) {
  try {
    const updateData = req.body;

    // Debug: Log the incoming data
    console.log('Update data received:', JSON.stringify(updateData, null, 2));

    // Remove id from updateData if it exists (since we get it from URL)
    delete updateData.id;

    // Validate required fields
    if (updateData.name !== undefined && !updateData.name) {
      return res.status(400).json({ error: 'Name cannot be empty' });
    }
    if (updateData.price !== undefined && (updateData.price === null || updateData.price === '')) {
      return res.status(400).json({ error: 'Price is required' });
    }

    // Handle field mapping and UUID fields
    // Map 'category' to 'category_id' if it exists
    if (updateData.category !== undefined) {
      updateData.category_id = updateData.category === '' ? null : updateData.category;
      delete updateData.category;
    }

    // Handle other UUID fields - convert empty strings to null
    const uuidFields = ['category_id', 'supplier_id'];
    uuidFields.forEach(field => {
      if (updateData[field] === '') {
        updateData[field] = null;
      }
    });

    // Debug: Log the processed data
    console.log('Processed update data:', JSON.stringify(updateData, null, 2));

    // Update product in database using Supabase directly
    const { data, error } = await supabaseAdmin
      .from('products')
      .update(updateData)
      .eq('id', id)
      .select();

    if (error) {
      throw error;
    }

    if (!data || data.length === 0) {
      return res.status(404).json({ error: 'Product not found' });
    }

    return res.status(200).json({ product: data[0] });
  } catch (error) {
    console.error('Error updating product:', error);
    return res.status(500).json({
      error: 'Failed to update product',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
}

/**
 * Delete a product
 */
async function deleteProduct(req, res, id) {
  try {
    // Check if product exists first
    const { data: existingProduct, error: checkError } = await supabaseAdmin
      .from('products')
      .select('id')
      .eq('id', id)
      .single();

    if (checkError) {
      if (checkError.code === 'PGRST116') {
        return res.status(404).json({ error: 'Product not found' });
      }
      throw checkError;
    }

    // Delete product from database using Supabase directly
    const { error } = await supabaseAdmin
      .from('products')
      .delete()
      .eq('id', id);

    if (error) {
      throw error;
    }

    return res.status(200).json({ message: 'Product deleted successfully' });
  } catch (error) {
    console.error('Error deleting product:', error);
    return res.status(500).json({
      error: 'Failed to delete product',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
}
